import { getSignedUrl } from "@aws-sdk/cloudfront-signer";
import fs from "fs";
import dotenv from "dotenv";
import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, PutCommand } from "@aws-sdk/lib-dynamodb";
import crypto from "crypto";

// Load environment variables
dotenv.config();
const client = new DynamoDBClient({ region: "us-east-1" });

// Wrap it with DocumentClient (for JSON marshalling)
const dynamodb = DynamoDBDocumentClient.from(client);

// Load your private key
const privateKey = fs.readFileSync(process.env.CLOUDFRONT_PRIVATE_KEY_PATH, "utf8");

export async function generateDirectSignedUrl(filePath, expiresIn = 60) {
    const url = `https://${process.env.CLOUDFRONT_DOMAIN}/${filePath}`;

    // Calculate expiration time more precisely, similar to Java Instant.now().plus()
    const now = new Date();
    const expirationTime = new Date(now.getTime() + (expiresIn * 1000));

    // Ensure we're working with a clean timestamp (remove milliseconds for consistency)
    expirationTime.setMilliseconds(0);

    console.log(`Generating signed URL:`);
    console.log(`  Current time: ${now.toISOString()}`);
    console.log(`  Expires at: ${expirationTime.toISOString()}`);
    console.log(`  Duration: ${expiresIn} seconds`);

    const signedUrl = getSignedUrl({
        url,
        keyPairId: process.env.CLOUDFRONT_PUBLIC_KEY_ID,
        privateKey,
        dateLessThan: expirationTime.toISOString(),
    });

    // Generate a custom token for Lambda@Edge validation
    const customToken = generateCustomToken(filePath, expirationTime);

    // Add custom token to the signed URL
    const urlWithToken = addTokenToUrl(signedUrl, customToken);
    console.log(urlWithToken)

    // Store token in DynamoDB for Lambda@Edge validation
    await storeTokenInDynamoDB(customToken, expirationTime);

    return urlWithToken;
}

function generateCustomToken(filePath, expirationTime) {
    // Generate a unique token (you can use crypto.randomUUID() or any method)
    const tokenData = {
        file: filePath,
        expires: expirationTime.getTime(),
        random: crypto.randomBytes(16).toString('hex')
    };

    // Create a hash-based token or use UUID
    return crypto.createHash('sha256')
        .update(JSON.stringify(tokenData))
        .digest('hex')
        .substring(0, 32); // Use first 32 chars
}

function addTokenToUrl(signedUrl, token) {
    const urlObj = new URL(signedUrl);
    urlObj.searchParams.append('token', token);
    return urlObj.toString();
}

async function storeTokenInDynamoDB(token, expirationTime) {
    // Store token in DynamoDB for Lambda@Edge validation

    const params = {
        TableName: 'cloudfront-one-time-tokens',
        Item: {
            token: token,
            expiresAt: expirationTime.getTime(),
            used: false,
            createdAt: Date.now()
        }
    };

    try {
        await dynamodb.send(new PutCommand(params));
        console.log("Item inserted successfully");
    } catch (err) {
        console.error("Error inserting item:", err);
    }
}
