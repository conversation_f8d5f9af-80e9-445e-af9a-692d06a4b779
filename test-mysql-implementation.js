import { generateDirectSignedUrl } from "./cloudfrontSigner.js";
import { generateDirectSignedUrlMySQL, validateTokenMySQL, markTokenAsUsedMySQL, cleanupExpiredTokensMySQL } from "./cloudfrontSignerMySQL.js";
import dotenv from "dotenv";

dotenv.config();

async function testImplementations() {
    console.log("🧪 Testing CloudFront Signer Implementations\n");
    
    const testFile = "test-file.pdf";
    const expiresIn = 300; // 5 minutes
    
    try {
        console.log("1️⃣ Testing DynamoDB Implementation:");
        console.log("=====================================");
        const dynamoUrl = await generateDirectSignedUrl(testFile, expiresIn);
        console.log("✅ DynamoDB URL generated successfully");
        console.log(`📄 File: ${testFile}`);
        console.log(`⏰ Expires in: ${expiresIn} seconds`);
        console.log(`🔗 URL: ${dynamoUrl.substring(0, 100)}...`);
        console.log();
        
        console.log("2️⃣ Testing MySQL Implementation:");
        console.log("=================================");
        const mysqlUrl = await generateDirectSignedUrlMySQL(testFile, expiresIn);
        console.log("✅ MySQL URL generated successfully");
        console.log(`📄 File: ${testFile}`);
        console.log(`⏰ Expires in: ${expiresIn} seconds`);
        console.log(`🔗 URL: ${mysqlUrl.substring(0, 100)}...`);
        console.log();
        
        // Extract token from MySQL URL for testing
        const mysqlUrlObj = new URL(mysqlUrl);
        const token = mysqlUrlObj.searchParams.get('token');
        
        if (token) {
            console.log("3️⃣ Testing MySQL Token Validation:");
            console.log("===================================");
            
            // Test token validation
            const validation = await validateTokenMySQL(token);
            console.log(`✅ Token validation result: ${validation.valid ? 'VALID' : 'INVALID'}`);
            if (validation.valid) {
                console.log(`📊 Token data:`, validation.tokenData);
            }
            console.log();
            
            console.log("4️⃣ Testing MySQL Token Usage:");
            console.log("==============================");
            
            // Mark token as used
            const marked = await markTokenAsUsedMySQL(token);
            console.log(`✅ Token marked as used: ${marked ? 'SUCCESS' : 'FAILED'}`);
            
            // Try to validate again (should fail)
            const revalidation = await validateTokenMySQL(token);
            console.log(`✅ Re-validation result: ${revalidation.valid ? 'VALID' : 'INVALID'} (should be INVALID)`);
            console.log(`📝 Reason: ${revalidation.reason}`);
            console.log();
        }
        
        console.log("5️⃣ Testing MySQL Cleanup:");
        console.log("=========================");
        const cleanedCount = await cleanupExpiredTokensMySQL();
        console.log(`✅ Cleanup completed: ${cleanedCount} tokens removed`);
        console.log();
        
        console.log("🎉 All tests completed successfully!");
        console.log("\n📋 Summary:");
        console.log("- DynamoDB implementation: ✅ Working");
        console.log("- MySQL implementation: ✅ Working");
        console.log("- Token validation: ✅ Working");
        console.log("- Token usage tracking: ✅ Working");
        console.log("- Cleanup functionality: ✅ Working");
        
    } catch (error) {
        console.error("❌ Test failed:", error);
        process.exit(1);
    }
}

// Run tests
testImplementations().catch(console.error);
