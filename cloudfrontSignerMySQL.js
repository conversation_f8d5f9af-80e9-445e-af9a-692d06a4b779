import { getSignedUrl } from "@aws-sdk/cloudfront-signer";
import fs from "fs";
import dotenv from "dotenv";
import mysql from "mysql2/promise";
import crypto from "crypto";

// Load environment variables
dotenv.config();

// MySQL connection configuration
const mysqlConfig = {
    host: process.env.MYSQL_HOST || 'localhost',
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || '',
    database: process.env.MYSQL_DATABASE || 'cloudfront_tokens',
    port: process.env.MYSQL_PORT || 3306,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
};

// Create MySQL connection pool
const pool = mysql.createPool(mysqlConfig);

// Load your private key
const privateKey = fs.readFileSync(process.env.CLOUDFRONT_PRIVATE_KEY_PATH, "utf8");

// Initialize MySQL table if it doesn't exist
async function initializeDatabase() {
    try {
        const connection = await pool.getConnection();
        
        // Create database if it doesn't exist
        await connection.execute(`CREATE DATABASE IF NOT EXISTS ${mysqlConfig.database}`);
        await connection.execute(`USE ${mysqlConfig.database}`);
        
        // Create table if it doesn't exist
        const createTableQuery = `
            CREATE TABLE IF NOT EXISTS cloudfront_one_time_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                token VARCHAR(64) NOT NULL UNIQUE,
                expires_at BIGINT NOT NULL,
                used BOOLEAN DEFAULT FALSE,
                created_at BIGINT NOT NULL,
                INDEX idx_token (token),
                INDEX idx_expires_at (expires_at),
                INDEX idx_used (used)
            )
        `;
        
        await connection.execute(createTableQuery);
        connection.release();
        
        console.log("MySQL database and table initialized successfully");
    } catch (error) {
        console.error("Error initializing MySQL database:", error);
        throw error;
    }
}

export async function generateDirectSignedUrlMySQL(filePath, expiresIn = 60) {
    const url = `https://${process.env.CLOUDFRONT_DOMAIN}/${filePath}`;

    // Calculate expiration time more precisely, similar to Java Instant.now().plus()
    const now = new Date();
    const expirationTime = new Date(now.getTime() + (expiresIn * 1000));

    // Ensure we're working with a clean timestamp (remove milliseconds for consistency)
    expirationTime.setMilliseconds(0);

    console.log(`Generating signed URL (MySQL):`);
    console.log(`  Current time: ${now.toISOString()}`);
    console.log(`  Expires at: ${expirationTime.toISOString()}`);
    console.log(`  Duration: ${expiresIn} seconds`);

    const signedUrl = getSignedUrl({
        url,
        keyPairId: process.env.CLOUDFRONT_PUBLIC_KEY_ID,
        privateKey,
        dateLessThan: expirationTime.toISOString(),
    });

    // Generate a custom token for Lambda@Edge validation
    const customToken = generateCustomToken(filePath, expirationTime);

    // Add custom token to the signed URL
    const urlWithToken = addTokenToUrl(signedUrl, customToken);
    console.log(urlWithToken);

    // Store token in MySQL for Lambda@Edge validation
    await storeTokenInMySQL(customToken, expirationTime);

    return urlWithToken;
}

function generateCustomToken(filePath, expirationTime) {
    // Generate a unique token (you can use crypto.randomUUID() or any method)
    const tokenData = {
        file: filePath,
        expires: expirationTime.getTime(),
        random: crypto.randomBytes(16).toString('hex')
    };

    // Create a hash-based token or use UUID
    return crypto.createHash('sha256')
        .update(JSON.stringify(tokenData))
        .digest('hex')
        .substring(0, 32); // Use first 32 chars
}

function addTokenToUrl(signedUrl, token) {
    const urlObj = new URL(signedUrl);
    urlObj.searchParams.append('token', token);
    return urlObj.toString();
}

async function storeTokenInMySQL(token, expirationTime) {
    // Store token in MySQL for Lambda@Edge validation
    try {
        const connection = await pool.getConnection();
        
        const insertQuery = `
            INSERT INTO cloudfront_one_time_tokens (token, expires_at, used, created_at)
            VALUES (?, ?, ?, ?)
        `;
        
        const values = [
            token,
            expirationTime.getTime(),
            false,
            Date.now()
        ];
        
        await connection.execute(insertQuery, values);
        connection.release();
        
        console.log("Token inserted successfully into MySQL");
    } catch (error) {
        console.error("Error inserting token into MySQL:", error);
        throw error;
    }
}

// Helper function to validate token (for Lambda@Edge or server-side validation)
export async function validateTokenMySQL(token) {
    try {
        const connection = await pool.getConnection();
        
        const selectQuery = `
            SELECT token, expires_at, used, created_at 
            FROM cloudfront_one_time_tokens 
            WHERE token = ? AND used = FALSE AND expires_at > ?
        `;
        
        const [rows] = await connection.execute(selectQuery, [token, Date.now()]);
        connection.release();
        
        if (rows.length === 0) {
            return { valid: false, reason: 'Token not found, expired, or already used' };
        }
        
        return { 
            valid: true, 
            tokenData: rows[0] 
        };
    } catch (error) {
        console.error("Error validating token in MySQL:", error);
        return { valid: false, reason: 'Database error' };
    }
}

// Helper function to mark token as used
export async function markTokenAsUsedMySQL(token) {
    try {
        const connection = await pool.getConnection();
        
        const updateQuery = `
            UPDATE cloudfront_one_time_tokens 
            SET used = TRUE 
            WHERE token = ? AND used = FALSE
        `;
        
        const [result] = await connection.execute(updateQuery, [token]);
        connection.release();
        
        return result.affectedRows > 0;
    } catch (error) {
        console.error("Error marking token as used in MySQL:", error);
        return false;
    }
}

// Helper function to cleanup expired tokens
export async function cleanupExpiredTokensMySQL() {
    try {
        const connection = await pool.getConnection();
        
        const deleteQuery = `
            DELETE FROM cloudfront_one_time_tokens 
            WHERE expires_at < ? OR used = TRUE
        `;
        
        const [result] = await connection.execute(deleteQuery, [Date.now()]);
        connection.release();
        
        console.log(`Cleaned up ${result.affectedRows} expired/used tokens from MySQL`);
        return result.affectedRows;
    } catch (error) {
        console.error("Error cleaning up expired tokens in MySQL:", error);
        return 0;
    }
}

// Initialize database on module load
initializeDatabase().catch(console.error);
