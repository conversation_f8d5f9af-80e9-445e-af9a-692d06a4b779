# CloudFront Signer - MySQL Implementation

This document describes the MySQL implementation of the CloudFront signed URL generator with one-time access tokens.

## 🆕 What's New

The MySQL implementation provides the **exact same functionality** as the DynamoDB version but stores tokens in a MySQL database instead. This gives you more flexibility in choosing your preferred database technology.

## 🔧 Setup

### 1. Install MySQL Dependencies

The MySQL dependency is already installed:
```bash
npm install mysql2
```

### 2. Configure MySQL Database

Add the following environment variables to your `.env` file:

```env
# MySQL Configuration for One-Time Access Tokens
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=cloudfront_tokens
MYSQL_PORT=3306
```

### 3. Database Initialization

The MySQL implementation automatically creates the database and table when the module is first loaded:

```sql
CREATE DATABASE IF NOT EXISTS cloudfront_tokens;

CREATE TABLE IF NOT EXISTS cloudfront_one_time_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires_at BIGINT NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at BIGINT NOT NULL,
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at),
    INDEX idx_used (used)
);
```

## 📡 API Usage

### New MySQL Endpoint

```http
GET /get-signed-url-mysql?file=/path/to/file.pdf&expires=300

# Response
{
  "message": "Signed URL generated with MySQL token storage",
  "signedUrl": "https://your-cloudfront-domain.com/file.pdf?Expires=...&token=abc123",
  "expiresIn": 300,
  "currentTime": "2024-01-01T12:00:00.000Z",
  "expiresAt": "2024-01-01T12:05:00.000Z",
  "storage": "MySQL"
}
```

### Comparison with DynamoDB Endpoint

| Feature | DynamoDB Endpoint | MySQL Endpoint |
|---------|------------------|----------------|
| URL | `/get-signed-url` | `/get-signed-url-mysql` |
| Functionality | ✅ Identical | ✅ Identical |
| Token Generation | ✅ Same algorithm | ✅ Same algorithm |
| Token Storage | DynamoDB | MySQL |
| Lambda@Edge Compatible | ✅ Yes | ✅ Yes |

## 🔍 Implementation Details

### Core Functions

The MySQL implementation provides these functions:

```javascript
// Generate signed URL with MySQL token storage
await generateDirectSignedUrlMySQL(filePath, expiresIn)

// Validate a token
await validateTokenMySQL(token)

// Mark token as used
await markTokenAsUsedMySQL(token)

// Cleanup expired tokens
await cleanupExpiredTokensMySQL()
```

### Token Storage Schema

```sql
-- Token storage table
cloudfront_one_time_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    token VARCHAR(64) NOT NULL UNIQUE,      -- 32-character SHA-256 hash
    expires_at BIGINT NOT NULL,             -- Unix timestamp in milliseconds
    used BOOLEAN DEFAULT FALSE,             -- Whether token has been consumed
    created_at BIGINT NOT NULL,             -- Creation timestamp
    -- Indexes for performance
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at),
    INDEX idx_used (used)
)
```

### Connection Pooling

The implementation uses MySQL connection pooling for optimal performance:

```javascript
const mysqlConfig = {
    host: process.env.MYSQL_HOST || 'localhost',
    user: process.env.MYSQL_USER || 'root',
    password: process.env.MYSQL_PASSWORD || '',
    database: process.env.MYSQL_DATABASE || 'cloudfront_tokens',
    port: process.env.MYSQL_PORT || 3306,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
};
```

## 🧪 Testing

Run the test script to verify both implementations work correctly:

```bash
node test-mysql-implementation.js
```

The test will:
1. ✅ Generate a signed URL using DynamoDB
2. ✅ Generate a signed URL using MySQL
3. ✅ Validate MySQL token
4. ✅ Mark MySQL token as used
5. ✅ Test token re-validation (should fail)
6. ✅ Test cleanup functionality

## 🔄 Migration

### From DynamoDB to MySQL

1. **Keep both implementations running** during migration
2. **Update Lambda@Edge functions** to check both DynamoDB and MySQL
3. **Gradually switch** new token generation to MySQL
4. **Monitor** both systems during transition
5. **Decommission** DynamoDB once migration is complete

### Database Migration Script

```sql
-- Export from DynamoDB (use AWS CLI or SDK)
-- Import to MySQL
INSERT INTO cloudfront_one_time_tokens (token, expires_at, used, created_at)
VALUES 
  ('token1', 1640995200000, false, 1640991600000),
  ('token2', 1640995200000, true, 1640991600000);
```

## 🚀 Performance Considerations

### MySQL Optimizations

1. **Indexes**: Proper indexing on `token`, `expires_at`, and `used` columns
2. **Connection Pooling**: Reuse database connections
3. **Cleanup Schedule**: Regular cleanup of expired tokens
4. **Partitioning**: Consider table partitioning for high-volume scenarios

### Monitoring

Monitor these MySQL metrics:
- Connection pool usage
- Query execution time
- Table size growth
- Index effectiveness

## 🔒 Security

The MySQL implementation maintains the same security features:

- ✅ **Same token generation algorithm** as DynamoDB version
- ✅ **SHA-256 hashed tokens** with cryptographic randomness
- ✅ **Automatic expiration** handling
- ✅ **One-time use** enforcement
- ✅ **Lambda@Edge compatible** token validation

## 🛠️ Troubleshooting

### Common Issues

**Connection Refused**
```bash
# Check MySQL service
sudo systemctl status mysql
# or
brew services list | grep mysql
```

**Database Not Found**
```bash
# The implementation auto-creates the database
# Check MySQL user permissions
GRANT ALL PRIVILEGES ON cloudfront_tokens.* TO 'your_user'@'localhost';
```

**Token Validation Fails**
```bash
# Check table structure
DESCRIBE cloudfront_tokens.cloudfront_one_time_tokens;

# Check token data
SELECT * FROM cloudfront_tokens.cloudfront_one_time_tokens WHERE token = 'your_token';
```

## 📊 Comparison Summary

| Aspect | DynamoDB | MySQL |
|--------|----------|-------|
| **Setup Complexity** | AWS Configuration | Local/Remote DB Setup |
| **Scalability** | Auto-scaling | Manual scaling |
| **Cost** | Pay-per-request | Fixed hosting cost |
| **Latency** | Very low | Low (depends on setup) |
| **Maintenance** | Managed service | Self-managed |
| **Backup** | Automatic | Manual/Automated |
| **Lambda@Edge** | Native support | Requires connection |

Both implementations are **functionally identical** and can be used interchangeably based on your infrastructure preferences.
