import mysql from "mysql2/promise";
import dotenv from "dotenv";

dotenv.config();

// MySQL connection configuration
const mysqlConfig = {
    host: process.env.MYSQL_HOST,
    user: process.env.MYSQL_USER,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE,
    port: process.env.MYSQL_PORT
};

async function examineDatabase() {
    let connection;
    
    try {
        console.log("🔌 Connecting to MySQL database...");
        console.log(`📍 Host: ${mysqlConfig.host}:${mysqlConfig.port}`);
        console.log(`👤 User: ${mysqlConfig.user}`);
        console.log(`🗄️ Database: ${mysqlConfig.database}`);
        console.log();
        
        connection = await mysql.createConnection(mysqlConfig);
        console.log("✅ Connected successfully!");
        console.log();
        
        // Show all tables in the database
        console.log("📋 Tables in database:");
        console.log("======================");
        const [tables] = await connection.execute("SHOW TABLES");
        tables.forEach((table, index) => {
            const tableName = Object.values(table)[0];
            console.log(`${index + 1}. ${tableName}`);
        });
        console.log();
        
        // Check if key_value_mst table exists
        const keyValueTableExists = tables.some(table => 
            Object.values(table)[0] === 'key_value_mst'
        );
        
        if (!keyValueTableExists) {
            console.log("❌ Table 'key_value_mst' not found!");
            return;
        }
        
        console.log("🔍 Examining 'key_value_mst' table structure:");
        console.log("=============================================");
        
        // Describe the table structure
        const [columns] = await connection.execute("DESCRIBE key_value_mst");
        console.log("📊 Table Structure:");
        console.log("Column Name".padEnd(20) + "Type".padEnd(20) + "Null".padEnd(8) + "Key".padEnd(8) + "Default".padEnd(15) + "Extra");
        console.log("-".repeat(80));
        
        columns.forEach(col => {
            console.log(
                col.Field.padEnd(20) + 
                col.Type.padEnd(20) + 
                col.Null.padEnd(8) + 
                (col.Key || '').padEnd(8) + 
                (col.Default || 'NULL').toString().padEnd(15) + 
                (col.Extra || '')
            );
        });
        console.log();
        
        // Show sample data
        console.log("📄 Sample data (first 5 rows):");
        console.log("===============================");
        const [sampleData] = await connection.execute("SELECT * FROM key_value_mst LIMIT 5");
        
        if (sampleData.length > 0) {
            // Show column headers
            const headers = Object.keys(sampleData[0]);
            console.log(headers.map(h => h.padEnd(20)).join(""));
            console.log("-".repeat(headers.length * 20));
            
            // Show data rows
            sampleData.forEach(row => {
                console.log(headers.map(h => (row[h] || 'NULL').toString().padEnd(20)).join(""));
            });
        } else {
            console.log("(No data found)");
        }
        console.log();
        
        // Show total row count
        const [countResult] = await connection.execute("SELECT COUNT(*) as total FROM key_value_mst");
        console.log(`📊 Total rows in table: ${countResult[0].total}`);
        console.log();
        
        // Check for indexes
        console.log("🔑 Table Indexes:");
        console.log("=================");
        const [indexes] = await connection.execute("SHOW INDEX FROM key_value_mst");
        
        if (indexes.length > 0) {
            console.log("Key Name".padEnd(20) + "Column".padEnd(20) + "Unique".padEnd(10) + "Type");
            console.log("-".repeat(60));
            
            indexes.forEach(idx => {
                console.log(
                    idx.Key_name.padEnd(20) + 
                    idx.Column_name.padEnd(20) + 
                    (idx.Non_unique === 0 ? 'YES' : 'NO').padEnd(10) + 
                    (idx.Index_type || '')
                );
            });
        } else {
            console.log("(No indexes found)");
        }
        console.log();
        
        console.log("✅ Database examination completed!");
        
    } catch (error) {
        console.error("❌ Error examining database:", error.message);
        if (error.code) {
            console.error(`Error Code: ${error.code}`);
        }
    } finally {
        if (connection) {
            await connection.end();
            console.log("🔌 Database connection closed.");
        }
    }
}

// Run the examination
examineDatabase().catch(console.error);
